2025-09-25 15:01:33.169[main][INFO][CSMSUtil] - Find CSMS SDK config file in class path csms.properties
2025-09-25 15:01:33.170[main][INFO][CSMSBeanFactory] - add CSMS shutdown hook
2025-09-25 15:01:33.191[main][INFO][CSMSServiceProvider] - CSMS SDK config {"isEnable":true,"rotateConfig":{"enable":true,"pollingInterval":60,"maxThreads":20,"maxQueueSize":20,"maxWait":300},"metricConfig":{"enable":true,"reportInterval":1800,"hotReloadReportEnable":true,"hotReloadReportInterval":240},"endpoints":"https://csmsdev.zoomdev.us","path":"dev/cube","durationSeconds":3600,"sectionName":"default","httpOptions":{"connectTimeout":5000,"readTimeout":10000,"callTimeout":15000,"maxRetry":3,"maxIdleConnections":20,"keepAliveDuration":5,"retrySleepMillSecond":200},"isEnableIMDSv2":true,"maxRegisterPublicPathsCount":2000,"verifyPublicPath":true,"clientSecretReportEnable":true,"secretsAgent":{"enable":false,"secrets":"","zoomNodeSocketName":"/opt/zoom/node/agent/ipc/uds.sock","zoomNodeTokenUrl":"","debugToken":""}}
2025-09-25 15:01:35.220[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-09-25 15:01:37.224[main][WARN][AuthServiceImpl] - Get instance bind role failed with error Connect timed out
2025-09-25 15:01:37.225[main][INFO][AuthServiceImpl] - Try to check local aws credentials file to judge deploy type
2025-09-25 15:01:37.230[main][INFO][AuthServiceImpl] - Judge EC2 deploy type. Inner deploy type=EC2
2025-09-25 15:01:37.233[main][INFO][SystemUtil] - Get process pid 32897
2025-09-25 15:01:37.235[main][WARN][SystemUtil] - Get SN from CMDB error Connection refused
2025-09-25 15:01:37.235[main][WARN][SystemUtil] - Get instanceId from CMDB error Connection refused
2025-09-25 15:01:37.236[main][WARN][SystemUtil] - Get process CMD from CMDB error Connection refused
2025-09-25 15:01:37.236[main][INFO][AuthServiceImpl] - Init refresh token task successful
2025-09-25 15:01:39.239[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-09-25 15:01:39.244[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-09-25 15:01:39.246[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Host is down
2025-09-25 15:01:39.242[main][ERROR][CSMSApplicationContextInitializer] - Get secrets from CSMS server failed
us.zoom.cloud.secrets.exception.CSMSCredException: Get EC2 STS failed: Host is down
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredKey(AuthServiceImpl.java:178)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredToken(AuthServiceImpl.java:155)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getToken(AuthServiceImpl.java:109)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:149)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:132)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.withRetryQuery(CSMSRestApiImpl.java:287)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.get(CSMSRestApiImpl.java:45)
	at us.zoom.cloud.secrets.service.impl.BaseCSMSService.getByPath(BaseCSMSService.java:61)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:35)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:26)
	at us.zoom.cloud.secrets.env.springboot.CSMSApplicationContextInitializer.initialize(CSMSApplicationContextInitializer.java:30)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at us.zoom.cschedule.schedule.CscheduleScheduleApplication.main(CscheduleScheduleApplication.java:21)
2025-09-25 15:01:39.248[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-09-25 15:01:39.249[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-09-25 15:01:39.249[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 3ms
2025-09-25 15:01:39.683[main][INFO][CSMSMultiServiceBridgeInitializer] - Init CSMSMultiServiceBridge successful
2025-09-25 15:01:39.683[main][WARN][CSMSMergeApplicationContextInitializer] - Ignore merge operate, because not find csmsPropertyResource
2025-09-25 15:01:39.684[main][INFO][CscheduleScheduleApplication] - Starting CscheduleScheduleApplication using Java 17.0.12 with PID 32897 (/Users/<USER>/IdeaGitProjects/cube-scheduler/cschedule-schedule/target/classes started by starls.ding in /Users/<USER>/IdeaGitProjects/cube-scheduler)
2025-09-25 15:01:39.684[main][INFO][CscheduleScheduleApplication] - No active profile set, falling back to 1 default profile: "default"
2025-09-25 15:01:40.453[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-09-25 15:01:40.454[main][INFO][CSMSHotReloadMetricServiceImpl] - Start report hot reload metric task successful
2025-09-25 15:01:40.455[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.username to map.
2025-09-25 15:01:40.455[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.password to map.
2025-09-25 15:01:40.455[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-09-25 15:01:40.456[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-09-25 15:01:40.456[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-09-25 15:01:40.464[main][INFO][MultipleSchedulerConfig] - load quartz properties from classpath successfully, properties: {org.quartz.scheduler.batchTriggerAcquisitionMaxCount=100, org.quartz.scheduler.instanceId=AUTO, org.quartz.threadPool.threadCount=100, org.quartz.jobStore.acquireTriggersWithinLock=true, org.quartz.scheduler.instanceName=quartzScheduler, org.quartz.jobStore.lockHandler.class=org.quartz.impl.jdbcjobstore.SimpleSemaphore, org.quartz.jobStore.tablePrefix=QRTZ_, org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow=10000}
2025-09-25 15:01:40.477[main][INFO][StdSchedulerFactory] - Using custom data access locking (synchronization): org.quartz.impl.jdbcjobstore.SimpleSemaphore
2025-09-25 15:01:40.477[main][INFO][StdSchedulerFactory] - Using default implementation for ThreadExecutor
2025-09-25 15:01:40.484[main][INFO][SchedulerSignalerImpl] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-09-25 15:01:40.485[main][INFO][QuartzScheduler] - Quartz Scheduler v.2.3.2 created.
2025-09-25 15:01:40.488[main][ERROR][HikariConfig] - cube-scheduler-datasource - dataSource or dataSourceClassName or jdbcUrl is required.
2025-09-25 15:01:40.488[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-09-25 15:01:40.488[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-09-25 15:01:40.488[main][DEBUG][SimpleThreadPool] - Shutting down threadpool...
2025-09-25 15:01:40.488[main][DEBUG][SimpleThreadPool] - Shutdown of threadpool complete.
2025-09-25 15:01:40.488[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-09-25 15:01:40.980[quartzScheduler_Worker-9][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-32][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.980[quartzScheduler_Worker-4][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-61][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.980[quartzScheduler_Worker-23][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-39][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-28][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.980[quartzScheduler_Worker-17][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-20][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.980[quartzScheduler_Worker-8][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-42][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-49][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-34][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-54][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-59][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-72][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.980[quartzScheduler_Worker-12][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-47][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.981[quartzScheduler_Worker-48][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.980[quartzScheduler_Worker-11][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-93][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-80][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-73][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-77][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-87][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-82][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-97][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-83][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-96][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-78][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-64][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-53][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-75][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-1][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-67][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-15][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-52][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-24][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-43][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-7][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-50][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-21][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-56][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-19][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-84][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-91][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-85][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-92][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-90][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-38][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.982[quartzScheduler_Worker-25][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-68][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-65][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-2][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-16][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-27][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-33][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-99][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-3][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-57][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-36][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-40][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-35][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-79][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-5][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-88][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-13][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-45][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-10][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-14][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-86][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-22][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-31][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-46][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-18][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-66][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-6][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-95][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-26][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-29][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-30][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-41][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-44][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-55][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-58][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.983[quartzScheduler_Worker-51][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-98][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-62][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-37][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-81][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-60][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.984[quartzScheduler_Worker-94][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-70][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-100][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-89][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-69][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-63][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-76][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-71][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:01:40.985[quartzScheduler_Worker-74][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-09-25 15:02:41.256[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-09-25 15:02:43.259[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-09-25 15:02:45.262[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-09-25 15:02:45.264[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-09-25 15:02:45.264[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2004ms
2025-09-25 15:03:47.269[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-09-25 15:03:49.271[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-09-25 15:03:51.272[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-09-25 15:03:51.273[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-09-25 15:03:51.273[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2002ms
2025-09-25 15:04:17.063[CSMS thread shutdown][INFO][CSMSBeanFactory] - start execute CSMS shutdown

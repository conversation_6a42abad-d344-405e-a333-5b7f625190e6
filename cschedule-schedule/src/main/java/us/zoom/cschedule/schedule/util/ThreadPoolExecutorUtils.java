package us.zoom.cschedule.schedule.util;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import java.util.concurrent.ThreadLocalRandom;
import us.zoom.cube.sdk.model.MonitorLog;

import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class ThreadPoolExecutorUtils {

    private static final String MEASURE_THREAD_POOL_METRIC = "thread_pool_metric";
    private static final Map<String, ThreadPoolExecutor> threadPoolExecutorMap = Maps.newHashMap();
    private static volatile ThreadPoolExecutor jobExecutionThreadPoolExecutor;

    static {
        // thread pool monitoring
        Executors.newSingleThreadScheduledExecutor()
                .scheduleAtFixedRate(() -> {
                            for (Map.Entry<String, ThreadPoolExecutor> entry : threadPoolExecutorMap.entrySet()) {
                                printThreadPoolMetricData(entry.getValue(), MEASURE_THREAD_POOL_METRIC, entry.getKey());
                            }
                        },
                        ThreadLocalRandom.current().nextInt(1, 6), 5, TimeUnit.SECONDS);
    }

    public static ThreadPoolExecutor getJobExecutionThreadPoolExecutor() {
        if (jobExecutionThreadPoolExecutor == null) {
            synchronized (ThreadPoolExecutorUtils.class) {
                if (jobExecutionThreadPoolExecutor == null) {
                    jobExecutionThreadPoolExecutor = buildJobExecutionThreadPoolExecutor("job-execution", new ThreadPoolExecutor.CallerRunsPolicy());
                    threadPoolExecutorMap.put("job-execution", jobExecutionThreadPoolExecutor);
                }
            }
        }
        return jobExecutionThreadPoolExecutor;
    }


    private static ThreadPoolExecutor buildJobExecutionThreadPoolExecutor(String threadPrefix, RejectedExecutionHandler handler) {
        return new ThreadPoolExecutor(
                20,
                20,
                10, TimeUnit.MINUTES,
                new LinkedBlockingQueue<>(1000),
                new ThreadFactoryBuilder().setNameFormat(threadPrefix + "-thread-%d").build(),
                handler);
    }

    public static void printThreadPoolMetricData(ThreadPoolExecutor threadPoolExecutor, String monitorLogMeasure, String threadPoolName) {
        long now = Instant.now().toEpochMilli();
        MonitorLogUtils.print(MonitorLog.builder()
                .withCubeVer("1.0.0")
                .withMeasure(monitorLogMeasure)
                .addTag("threadPoolName", threadPoolName)
                .addField("readableTs", DateUtils.format(new Date(now), DateUtils.FORMART1))
                .addField("corePoolSize", threadPoolExecutor.getCorePoolSize())
                .addField("maxPoolSize", threadPoolExecutor.getMaximumPoolSize())
                .addField("queueSize", threadPoolExecutor.getQueue().size())
                .addField("activeCount", threadPoolExecutor.getActiveCount())
                .addField("taskCount", threadPoolExecutor.getTaskCount())
                .addField("completedTaskCount", threadPoolExecutor.getCompletedTaskCount())
                .withTs(now)
                .build());
    }
}
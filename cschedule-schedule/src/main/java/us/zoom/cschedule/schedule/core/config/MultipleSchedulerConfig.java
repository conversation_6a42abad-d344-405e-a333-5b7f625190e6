package us.zoom.cschedule.schedule.core.config;

import org.apache.commons.lang3.StringUtils;
import org.quartz.Scheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.sql.DataSource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * @author: eason.jia
 * @date: 2024/12/29
 */
@Configuration
public class MultipleSchedulerConfig {

    private static final Logger logger = LoggerFactory.getLogger(MultipleSchedulerConfig.class);
    private static final String QUARTZ_SCHEDULER_NAME_KEY = "org.quartz.scheduler.instanceName";
    private static final String QUARTZ_PROPERTIES_PATH = "/config";

    @Autowired
    private DataSource dataSource;
    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Value("${quartz.start.scheduler.instance.name}")
    private String startSchedulerInstanceName;

    /**
     * primary partition
     *
     * @return
     * @throws IOException
     */
    @Bean("schedulerFactoryBean")
    @Primary
    public SchedulerFactoryBean schedulerFactoryBean() throws IOException {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        schedulerFactoryBean.setDataSource(dataSource);
        schedulerFactoryBean.setTransactionManager(dataSourceTransactionManager);
        Properties quartzProperties = loadQuartzProperties("quartz-1.properties");
        schedulerFactoryBean.setQuartzProperties(quartzProperties);
        schedulerFactoryBean.setAutoStartup(StringUtils.equalsAnyIgnoreCase(quartzProperties.getProperty(QUARTZ_SCHEDULER_NAME_KEY), startSchedulerInstanceName));
        return schedulerFactoryBean;
    }

    /**
     * primary partition
     *
     * @param schedulerFactoryBean
     * @return
     */
    @Bean("scheduler")
    @Primary
    public Scheduler scheduler(@Qualifier("schedulerFactoryBean") SchedulerFactoryBean schedulerFactoryBean) {
        return schedulerFactoryBean.getScheduler();
    }

    /**
     * secondary partition
     *
     * @return
     * @throws IOException
     */
    @Bean("schedulerFactoryBean2")
    public SchedulerFactoryBean schedulerFactoryBean2() throws IOException {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        schedulerFactoryBean.setDataSource(dataSource);
        schedulerFactoryBean.setTransactionManager(dataSourceTransactionManager);
        Properties quartzProperties = loadQuartzProperties("quartz-2.properties");
        schedulerFactoryBean.setQuartzProperties(quartzProperties);
        schedulerFactoryBean.setAutoStartup(StringUtils.equalsAnyIgnoreCase(quartzProperties.getProperty(QUARTZ_SCHEDULER_NAME_KEY), startSchedulerInstanceName));
        return schedulerFactoryBean;
    }

    /**
     * secondary partition
     *
     * @param schedulerFactoryBean2
     * @return
     */
    @Bean("scheduler2")
    public Scheduler scheduler2(@Qualifier("schedulerFactoryBean2") SchedulerFactoryBean schedulerFactoryBean2) {
        return schedulerFactoryBean2.getScheduler();
    }

    /**
     * third partition
     *
     * @return
     * @throws IOException
     */
    @Bean("schedulerFactoryBean3")
    public SchedulerFactoryBean schedulerFactoryBean3() throws IOException {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        schedulerFactoryBean.setDataSource(dataSource);
        schedulerFactoryBean.setTransactionManager(dataSourceTransactionManager);
        Properties quartzProperties = loadQuartzProperties("quartz-3.properties");
        schedulerFactoryBean.setQuartzProperties(quartzProperties);
        schedulerFactoryBean.setAutoStartup(StringUtils.equalsAnyIgnoreCase(quartzProperties.getProperty(QUARTZ_SCHEDULER_NAME_KEY), startSchedulerInstanceName));
        return schedulerFactoryBean;
    }

    /**
     * third partition
     *
     * @param schedulerFactoryBean3
     * @return
     */
    @Bean("scheduler3")
    public Scheduler scheduler3(@Qualifier("schedulerFactoryBean3") SchedulerFactoryBean schedulerFactoryBean3) {
        return schedulerFactoryBean3.getScheduler();
    }

    /**
     * 4th partition
     *
     * @return
     * @throws IOException
     */
    @Bean("schedulerFactoryBean4")
    public SchedulerFactoryBean schedulerFactoryBean4() throws IOException {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        schedulerFactoryBean.setDataSource(dataSource);
        schedulerFactoryBean.setTransactionManager(dataSourceTransactionManager);
        Properties quartzProperties = loadQuartzProperties("quartz-4.properties");
        schedulerFactoryBean.setQuartzProperties(quartzProperties);
        schedulerFactoryBean.setAutoStartup(StringUtils.equalsAnyIgnoreCase(quartzProperties.getProperty(QUARTZ_SCHEDULER_NAME_KEY), startSchedulerInstanceName));
        return schedulerFactoryBean;
    }

    /**
     * 4th partition
     *
     * @param schedulerFactoryBean4
     * @return
     */
    @Bean("scheduler4")
    public Scheduler scheduler4(@Qualifier("schedulerFactoryBean4") SchedulerFactoryBean schedulerFactoryBean4) {
        return schedulerFactoryBean4.getScheduler();
    }

    /**
     * 5th partition
     *
     * @return
     * @throws IOException
     */
    @Bean("schedulerFactoryBean5")
    public SchedulerFactoryBean schedulerFactoryBean5() throws IOException {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        schedulerFactoryBean.setDataSource(dataSource);
        schedulerFactoryBean.setTransactionManager(dataSourceTransactionManager);
        Properties quartzProperties = loadQuartzProperties("quartz-5.properties");
        schedulerFactoryBean.setQuartzProperties(quartzProperties);
        schedulerFactoryBean.setAutoStartup(StringUtils.equalsAnyIgnoreCase(quartzProperties.getProperty(QUARTZ_SCHEDULER_NAME_KEY), startSchedulerInstanceName));
        return schedulerFactoryBean;
    }

    /**
     * 5th partition
     *
     * @param schedulerFactoryBean5
     * @return
     */
    @Bean("scheduler5")
    public Scheduler scheduler5(@Qualifier("schedulerFactoryBean5") SchedulerFactoryBean schedulerFactoryBean5) {
        return schedulerFactoryBean5.getScheduler();
    }

    private Properties loadQuartzProperties(String filename) {
        Properties properties = new Properties();
        String configPath = QUARTZ_PROPERTIES_PATH + "/" + filename;

        File file = new File(configPath);
        if (file.exists()) {
            try (InputStream inputStream = new FileInputStream(file)) {
                properties.load(inputStream);
                logger.info("load quartz properties from path: [{}] successfully, properties: {}", configPath, properties);
            } catch (Exception e) {
                logger.error("fail to load quartz properties file: [{}]", configPath, e);
            }
        } else {
            try (InputStream classpathStream = new ClassPathResource(filename).getInputStream()) {
                properties.load(classpathStream);
                logger.info("load quartz properties from classpath successfully, properties: {}", properties);
            } catch (Exception e) {
                logger.error("fail to load quartz properties: [{}] from classpath", filename, e);
            }
        }
        return properties;
    }
}

package us.zoom.cschedule.schedule.biz.service.api;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cschedule.schedule.core.quartz.QuartzJobService;
import us.zoom.cschedule.schedule.core.quartz.QuartzSchedulerRouteService;
import us.zoom.cschedule.schedule.lib.ResponseObject;
import us.zoom.cube.lib.probe.SchedulerJobCfg;


/**
 * <AUTHOR>
 * @date 2022-06-08 10:58 
 */
@Slf4j
@RestController
@RequestMapping(value = "/job")
public class QuartzController {

    private static final String E2E_JOB_GROUP_NAME = "cube_probe";

    @Autowired
    private QuartzJobService quartzJobService;
    @Autowired
    private QuartzSchedulerRouteService quartzSchedulerRouteService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addJob(@RequestBody JobInfo jobInfo) {
        log.info("add job info:{}", jobInfo);
        quartzJobService.addJob(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    @RequestMapping(value = "/updateJobCron", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateJobCron(@RequestBody JobInfo jobInfo) throws SchedulerException {
        log.info("updateJobCron jobInfo:{}", jobInfo);
        quartzJobService.updateJobCron(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    @RequestMapping(value = "/updateByCron", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateJobByCron(@RequestBody JobInfo jobInfo) throws SchedulerException {
        log.info("updateJobByCron jobInfo:{}", jobInfo);
        quartzJobService.updateJobByCron(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    @RequestMapping(value = "/updateBySimple", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateJobBySimple(@RequestBody JobInfo jobInfo) throws SchedulerException {
        log.info("updateJobBySimple jobInfo:{}", jobInfo);
        quartzJobService.updateJobBySimple(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }


    @RequestMapping(value = "/pause", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject pauseJob(@RequestBody JobInfo jobInfo)
            throws SchedulerException {
        log.info("pauseJob jobInfo:{}", jobInfo);
        return quartzJobService.pauseJob(jobInfo);
    }

    @RequestMapping(value = "/resume", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject resumeJob(@RequestBody JobInfo jobInfo)
            throws SchedulerException {
        log.info("resumeJob jobInfo:{}", jobInfo);
        return quartzJobService.resumeJob(jobInfo);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject deleteJob(@RequestBody JobInfo jobInfo)
            throws SchedulerException {
        log.info("deleteJob jobInfo:{}", jobInfo);
        return quartzJobService.deleteJob(jobInfo);
    }

    @RequestMapping(value = "/getJobInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getJobInfo(@RequestBody JobInfo jobInfo)
            throws SchedulerException {
        log.info("getJobInfo jobInfo:{}", jobInfo);
        return quartzJobService.getJobInfo(jobInfo);
    }

    @RequestMapping(value = "/migrateJob", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject migrateJob(@RequestBody JobInfo jobInfo) {
        log.info("migrateJob jobInfo: {}", jobInfo);
        if (!canMigrateJob(jobInfo)) {
            return ResponseObject.success("there is no service mapping, don't migrate job");
        }
        return quartzJobService.migrationJob(jobInfo);
    }

    private boolean canMigrateJob(JobInfo jobInfo) {
        if (jobInfo == null || !StringUtils.equals(E2E_JOB_GROUP_NAME, jobInfo.getJobGroup())) {
            return false;
        }

        try {
            SchedulerJobCfg schedulerJobCfg = JsonUtils.toObject(jobInfo.getConfig(), SchedulerJobCfg.class);
            String serviceName = schedulerJobCfg.getServiceName();
            return quartzSchedulerRouteService.hasServiceMapping(serviceName);
        } catch (Exception e) {
            log.error("failed to judge whether need to migration, jobInfo: {}", jobInfo, e);
            return false;
        }
    }

    @RequestMapping(value = "/migrateHubSchedulerJob", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject migrateHubSchedulerJob(@RequestBody JobInfo jobInfo) {
        log.info("migrateHubSchedulerJob jobInfo: {}", jobInfo);
        return quartzJobService.migrationJob(jobInfo);
    }
}

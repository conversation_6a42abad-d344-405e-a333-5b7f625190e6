package us.zoom.cschedule.schedule.core.quartz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Scheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cube.lib.probe.SchedulerJobCfg;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/12/29
 */
@Service
public class QuartzSchedulerRouteService {

    private static final Logger logger = LoggerFactory.getLogger(QuartzSchedulerRouteService.class);
    private static final String E2E_JOB_GROUP_NAME = "cube_probe";
    private static final String HUB_SCHEDULER_JOB_GROUP_NAME = "hub_scheduler";
    private static final String DEFAULT_SCHEDULER_BEAN_NAME = "scheduler";
    private Map<String/**service name*/, String/**scheduler bean name*/> serviceSchedulerMapping = Maps.newHashMap();
    /**
     * Only for E2E
     * used for migrating from non-default scheduler to another scheduler(Not first migration)
     * For example, the first migration was from scheduler to scheduler2, and now there's a need to migrate from scheduler2 to scheduler3.
     * In this case, the previous scheduler of the current service is scheduler2.
     *
     */
    private Map<String/**service name*/, String/**previous scheduler bean name*/> previousServiceSchedulerMapping = Maps.newHashMap();
    private String hubSchedulerSchedulerName = "";

    @Autowired
    private Map<String/**scheduler bean name*/, Scheduler> schedulerMap;

    public Scheduler getScheduler(JobInfo jobInfo) {
        if (jobInfo == null) {
            return getDefaultScheduler();
        }
        // hub-scheduler
        if (StringUtils.equals(HUB_SCHEDULER_JOB_GROUP_NAME, jobInfo.getJobGroup())) {
            return StringUtils.isBlank(hubSchedulerSchedulerName) ? getDefaultScheduler() : schedulerMap.get(hubSchedulerSchedulerName);
        }
        // others but not E2E, for example: inspection(sharp-eyes)
        if (!StringUtils.equals(E2E_JOB_GROUP_NAME, jobInfo.getJobGroup())) {
            return getDefaultScheduler();
        }
        // E2E
        if (StringUtils.isBlank(jobInfo.getConfig())) {
            return getDefaultScheduler();
        }

        try {
            SchedulerJobCfg schedulerJobCfg = JsonUtils.toObject(jobInfo.getConfig(), SchedulerJobCfg.class);
            String serviceName = schedulerJobCfg.getServiceName();
            String schedulerName = serviceSchedulerMapping.getOrDefault(serviceName, DEFAULT_SCHEDULER_BEAN_NAME);
            return schedulerMap.get(schedulerName);
        } catch (Exception e) {
            logger.error("failed to get scheduler, jobInfo: {}", jobInfo, e);
            return getDefaultScheduler();
        }
    }

    /**
     * this method will be called during the migration process
     *
     * @param jobInfo
     * @return
     */
    public Scheduler getFallbackScheduler(JobInfo jobInfo) {
        if (jobInfo == null) {
            return getDefaultScheduler();
        }
        // others but not E2E, for example: hub-scheduler, inspection(sharp-eyes)
        if (!StringUtils.equals(E2E_JOB_GROUP_NAME, jobInfo.getJobGroup())) {
            return getDefaultScheduler();
        }
        // E2E
        if (StringUtils.isBlank(jobInfo.getConfig())) {
            return getDefaultScheduler();
        }

        try {
            SchedulerJobCfg schedulerJobCfg = JsonUtils.toObject(jobInfo.getConfig(), SchedulerJobCfg.class);
            String serviceName = schedulerJobCfg.getServiceName();
            return getPreviousScheduler(serviceName);
        } catch (Exception e) {
            logger.error("failed to get fallback scheduler, jobInfo: {}", jobInfo, e);
            return getDefaultScheduler();
        }
    }

    public Scheduler getPreviousScheduler(String serviceName) {
        String previousSchedulerName = previousServiceSchedulerMapping.get(serviceName);
        return StringUtils.isBlank(previousSchedulerName) ? getDefaultScheduler() : schedulerMap.get(previousSchedulerName);
    }

    public Scheduler getDefaultScheduler() {
        return schedulerMap.get(DEFAULT_SCHEDULER_BEAN_NAME);
    }

    public boolean hasServiceMapping(String serviceName) {
        return serviceSchedulerMapping.containsKey(serviceName);
    }

    public void refreshServiceSchedulerMapping(String sysPara) {
        try {
            Map<String, String> mapping = JsonUtils.toObjectByTypeRef(sysPara, new TypeReference<Map<String, String>>() {
            });
            serviceSchedulerMapping = mapping;
            logger.info("refresh service scheduler mapping, serviceSchedulerMapping: {}", serviceSchedulerMapping);
        } catch (Exception e) {
            logger.error("failed to deserialize system param: {}", sysPara, e);
        }
    }

    public void refreshPreviousServiceSchedulerMapping(String sysPara) {
        try {
            Map<String, String> mapping = JsonUtils.toObjectByTypeRef(sysPara, new TypeReference<Map<String, String>>() {
            });
            previousServiceSchedulerMapping = mapping;
            logger.info("refresh previous service scheduler mapping, previousServiceSchedulerMapping: {}", previousServiceSchedulerMapping);
        } catch (Exception e) {
            logger.error("failed to deserialize system param: {}", sysPara, e);
        }
    }

    public void refreshHubSchedulerSchedulerName(String sysPara) {
        hubSchedulerSchedulerName = sysPara;
        logger.info("refresh hub scheduler scheduler name: {}", hubSchedulerSchedulerName);
    }
}
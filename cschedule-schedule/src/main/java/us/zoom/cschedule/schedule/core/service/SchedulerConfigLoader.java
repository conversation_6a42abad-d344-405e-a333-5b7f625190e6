package us.zoom.cschedule.schedule.core.service;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.quartz.QuartzSchedulerRouteService;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.dao.model.SysParaDO;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class SchedulerConfigLoader {

    private static final Logger logger = LoggerFactory.getLogger(SchedulerConfigLoader.class);
    private static final String SYSTEM_PARAM_TYPE = "cube-scheduler";
    private static final String SERVICE_SCHEDULER_MAPPING_KEY = "serviceSchedulerMapping";
    private static final String PREVIOUS_SERVICE_SCHEDULER_MAPPING_KEY = "previousServiceSchedulerMapping";
    private static final String HUB_SCHEDULER_SCHEDULER_NAME = "hubSchedulerSchedulerName";

    @Autowired
    private ConfigApi configApi;
    @Autowired
    private QuartzSchedulerRouteService quartzSchedulerRouteService;

    @Scheduled(timeUnit = TimeUnit.MINUTES, fixedRate = 1)
    public void loadConfig() {
        List<SysParaDO> sysParaDOList = configApi.getSysParaByTypes(Lists.newArrayList(SYSTEM_PARAM_TYPE));
        logger.info("load system param: {}", JsonUtils.toJsonStringIgnoreExp(sysParaDOList));
        for (SysParaDO sysParaDO : sysParaDOList) {
            if (StringUtils.equalsIgnoreCase(sysParaDO.getParaKey(), SERVICE_SCHEDULER_MAPPING_KEY)) {
                quartzSchedulerRouteService.refreshServiceSchedulerMapping(sysParaDO.getValue());
            }
            if (StringUtils.equalsIgnoreCase(sysParaDO.getParaKey(), PREVIOUS_SERVICE_SCHEDULER_MAPPING_KEY)) {
                quartzSchedulerRouteService.refreshPreviousServiceSchedulerMapping(sysParaDO.getValue());
            }
            if (StringUtils.equalsIgnoreCase(sysParaDO.getParaKey(), HUB_SCHEDULER_SCHEDULER_NAME)) {
                quartzSchedulerRouteService.refreshHubSchedulerSchedulerName(sysParaDO.getValue());
            }
        }
    }
}

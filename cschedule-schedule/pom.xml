<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>cschedule</artifactId>
        <groupId>us.zoom</groupId>
        <version>0.0.1</version>
    </parent>

    <artifactId>cschedule-schedule</artifactId>

    <dependencies>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- mysql -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>9.3.0</version>
        </dependency>

        <!-- zoom -->
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>cube-config-client</artifactId>
            <version>2.0.15-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>cschedule-infra</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-web-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom.mq</groupId>
            <artifactId>asyncmq-client-ha</artifactId>
        </dependency>

        <!-- csms && jwt -->
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>csms-sdk-bridge-common</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>cloud-secrets-management-service-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>csms-sdk-api</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-commons-jwt</artifactId>
        </dependency>

        <!-- utils -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-web-cache</artifactId>
            <version>1.6.0826</version>
        </dependency>

        <!-- opentelemetry -->
        <dependency>
            <groupId>io.opentelemetry.instrumentation</groupId>
            <artifactId>opentelemetry-instrumentation-annotations</artifactId>
            <version>2.11.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>cschedule-schedule</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.9</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
        </plugins>
    </build>

</project>

#!/usr/bin/env sh

set -e

if [ -z "$MODULE_NAME" ]; then
  mvn install -Dmaven.test.skip=true -ntp -Dartifactory.publish.artifacts=false
else
  #sh ./ci/build_$MODULE_NAME.sh

  mkdir -p ${MODULE_NAME}/src/main/resources/static/
  mkdir -p output/report

  if [ -f "version.txt" ]; then
    cp version.txt ${MODULE_NAME}/src/main/resources/static/version.txt
  fi

  # build executable jar and move results into 'output'
  #mvn -f cube-scheduler-admin clean package -Dmaven.test.skip=true
  mvn install -pl ${MODULE_NAME} -am  -Dmaven.test.skip=true -ntp -Dartifactory.publish.artifacts=false
  cp ${MODULE_NAME}/target/${MODULE_NAME}.jar output/${MODULE_NAME}-"${VERSION}"."${BUILD_NUMBER}".jar
  #here we make a deal with Build team for final Dockerfile naming with module name
  cp -f ${MODULE_NAME}/ci/Dockerfile ./ci/Dockerfile-${MODULE_NAME}

fi
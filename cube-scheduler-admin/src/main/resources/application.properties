spring.application.name=cube-scheduler-admin

spring.profiles.active=debug

server.port=8080
server.ssl.enabled=false

# MySQL data source
dataSource.maxActive=5
dataSource.minIdle=1
# set in CSMS
#cube.dataSource.username=
#cube.dataSource.password=

mybatis.configuration.default-enum-type-handler=org.apache.ibatis.type.EnumTypeHandler
mybatis.configuration.map-underscore-to-camel-case=true

cube.scheduler.triggerpool.fast.max=200
cube.scheduler.triggerpool.slow.max=100


asyncmq.username=app_cube_scheduler
package us.zoom.schedulery.monitors.entity;

import lombok.Data;
import us.zoom.cube.scheduler.lib.enums.ChannelMedia;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.schedulery.enums.TriggerTypeEnum;

import java.util.HashMap;

@Data
public class TriggerMonitor extends AbstractBaseMonitor {

    private String jobId;

    private String scheduleId;

    private String jobName;

    private String tenantId;

    private boolean fastPool;

    private TriggerTypeEnum triggerType;

    private long cost;

    private boolean triggerFailed;

    private String failReason;

    private boolean failRetry;

    private ChannelMedia channelMedia;

    @Override
    public MonitorLog buildMonitorLog() {
        return MonitorLog.builder()
                .withMeasure("trigger_monitor")
                .withNewTags(new HashMap<>(){{
                    put("jobId", jobId);
                    put("jobName", jobName);
                    put("tenantId", tenantId);
                    put("triggerType", triggerType.name());
                    put("fastPool", fastPool);
                    put("triggerFailed", triggerFailed);
                    put("failRetry", failRetry);
                    put("channelMedia", channelMedia.name());
                }})
                .withNewFields(new HashMap<>(){{
                    put("scheduleId", scheduleId);
                    put("cost", cost);
                    put("failReason", failReason);
                }})
                .withTs(ts)
                .build();
    }

}

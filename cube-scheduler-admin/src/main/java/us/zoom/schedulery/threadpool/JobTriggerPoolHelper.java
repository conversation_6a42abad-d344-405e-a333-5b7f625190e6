package us.zoom.schedulery.threadpool;


import lombok.extern.slf4j.Slf4j;
import us.zoom.schedulery.config.SchedulerConfig;
import us.zoom.schedulery.entity.SimpleJobInfo;
import us.zoom.schedulery.enums.TriggerTypeEnum;
import us.zoom.schedulery.monitors.AsyncMonitorLogger;
import us.zoom.schedulery.monitors.entity.TriggerCreateMonitor;
import us.zoom.schedulery.monitors.entity.TriggerMonitor;
import us.zoom.schedulery.process.trigger.RealJobTrigger;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class JobTriggerPoolHelper {

    // ---------------------- trigger pool ----------------------
    public static final int DEFAULT_CORE_POOL_SIZE = 10;
    public static final int KEEP_ALIVE_TIME = 60;
    public static final int FAST_TRIGGER_QUEUE_CAPACITY = 2000;
    public static final int SLOW_TRIGGER_QUEUE_CAPACITY = 5000;

    // fast/slow thread pool
    private ThreadPoolExecutor fastTriggerPool = null;
    private ThreadPoolExecutor slowTriggerPool = null;

    public void start() {
        fastTriggerPool = new ThreadPoolExecutor(
                DEFAULT_CORE_POOL_SIZE,
                SchedulerConfig.getConfig().getTriggerPoolFastMax(),
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(FAST_TRIGGER_QUEUE_CAPACITY),
                r -> new Thread(r, "FastTriggerPool-" + r.hashCode()),
                (r, executor) -> {
                    //todo
                    log.error(">>>>>>>>>>> JobTriggerPoolHelper-fastTriggerPool execute too fast, Runnable=" + r.toString());
                });

        slowTriggerPool = new ThreadPoolExecutor(
                DEFAULT_CORE_POOL_SIZE,
                SchedulerConfig.getConfig().getTriggerPoolSlowMax(),
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(SLOW_TRIGGER_QUEUE_CAPACITY),
                r -> new Thread(r, "SlowTriggerPool-" + r.hashCode()),
                (r, executor) -> log.error(">>>>>>>>>>> xxl-job, admin JobTriggerPoolHelper-slowTriggerPool execute too fast, Runnable=" + r.toString()));
    }


    public void stop() {
        fastTriggerPool.shutdownNow();
        slowTriggerPool.shutdownNow();
        log.info(">>>>>>>>> Both trigger(fast/slow) thread pools shutdown success. ");
    }


    // job timeout count
    private volatile long minTim = System.currentTimeMillis() / 60000;     // ms > min
    private volatile ConcurrentMap<String, AtomicInteger> jobTimeoutCountMap = new ConcurrentHashMap<>();


    /**
     * add trigger
     */
    public void addToTrigger(final SimpleJobInfo simpleJobInfo,
                             final TriggerTypeEnum triggerType,
                             final int failRetryCount,
                             final String executorShardingParam,
                             final String executorParam,
                             final String addressList) {

        final String jobId = simpleJobInfo.getJobId();
        // choose thread pool
        ThreadPoolExecutor triggerPool_ = fastTriggerPool;
        AtomicInteger jobTimeoutCount = jobTimeoutCountMap.get(simpleJobInfo.getJobId());
        if (jobTimeoutCount != null && jobTimeoutCount.get() > 10) {      // job-timeout 10 times in 1 min
            triggerPool_ = slowTriggerPool;
        }

        final TriggerMonitor triggerMonitor = buildTriggerMonitor(simpleJobInfo, triggerType, triggerPool_ == fastTriggerPool);

        // trigger
        triggerPool_.execute(new Runnable() {
            @Override
            public void run() {
                long start = System.currentTimeMillis();
                triggerMonitor.setTs(start);

                try {
                    // do trigger
                    RealJobTrigger.trigger(simpleJobInfo, triggerType, failRetryCount, executorParam, addressList, triggerMonitor);
                } catch (Throwable e) {
                    triggerMonitor.setTriggerFailed(true);
                    triggerMonitor.setFailReason(e.getMessage());
                    log.error(e.getMessage(), e);
                } finally {
                    //todo maybe use another check thread to do this ?

                    // check timeout-count-map
                    long minTim_now = System.currentTimeMillis() / 60000;
                    if (minTim != minTim_now) {
                        minTim = minTim_now;
                        jobTimeoutCountMap.clear();
                    }

                    // incr timeout-count-map
                    long cost = System.currentTimeMillis() - start;

                    if (cost > 500) {       // ob-timeout threshold 500ms
                        AtomicInteger timeoutCount = jobTimeoutCountMap.putIfAbsent(jobId, new AtomicInteger(1));
                        if (timeoutCount != null) {
                            timeoutCount.incrementAndGet();
                        }
                        //todo to monitor this slow situation
                    }

                    triggerMonitor.setCost(cost);
                    AsyncMonitorLogger.log(triggerMonitor);

                }

            }

            @Override
            public String toString() {
                return "Job Runnable, jobId:" + jobId;
            }
        });
    }


    // ---------------------- helper ----------------------

    private static JobTriggerPoolHelper helper = new JobTriggerPoolHelper();

    public static void toStart() {
        helper.start();
    }

    public static void toStop() {
        helper.stop();
    }

    /**
     * @param simpleJobInfo
     * @param triggerType
     * @param failRetryCount
     * 			>=0: use this param
     * 			<0: use param from job info config
     * @param executorShardingParam
     * @param executorParam
     *          null: use job param
     *          not null: cover job param
     */
    public static void trigger(SimpleJobInfo simpleJobInfo, TriggerTypeEnum triggerType, int failRetryCount, String executorShardingParam, String executorParam, String addressList) {
        AsyncMonitorLogger.log(new TriggerCreateMonitor(simpleJobInfo, System.currentTimeMillis()));
        helper.addToTrigger(simpleJobInfo, triggerType, failRetryCount, executorShardingParam, executorParam, addressList);
    }

    public TriggerMonitor buildTriggerMonitor(SimpleJobInfo simpleJobInfo, TriggerTypeEnum triggerType, boolean fastPool) {
        TriggerMonitor triggerMonitor = new TriggerMonitor();
        triggerMonitor.setJobId(simpleJobInfo.getJobId());
        triggerMonitor.setScheduleId(simpleJobInfo.getScheduleId());
        triggerMonitor.setJobName(simpleJobInfo.getJobName());
        triggerMonitor.setTenantId(simpleJobInfo.getTenantId());
        triggerMonitor.setTriggerType(triggerType);
        triggerMonitor.setFastPool(fastPool);
        return triggerMonitor;
    }

}

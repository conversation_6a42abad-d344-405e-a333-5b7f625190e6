package us.zoom.schedulery.entity;

import lombok.Data;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;

@Data
public class SimpleJobInfo {

    private String jobId;

    private String jobName;

    private String scheduleId;

    private String tenantId;

    private long triggerLastTime;
    private long triggerNextTime;

    public SimpleJobInfo() {
    }

    public SimpleJobInfo(String jobId, String jobName, String scheduleId, String tenantId, long triggerLastTime, long triggerNextTime) {
        this.jobId = jobId;
        this.jobName = jobName;
        this.scheduleId = scheduleId;
        this.tenantId = tenantId;
        this.triggerLastTime = triggerLastTime;
        this.triggerNextTime = triggerNextTime;
    }

    public static SimpleJobInfo create(SchedulerJobInfo jobInfo, String scheduleId) {
        return new SimpleJobInfo(jobInfo.getId(), jobInfo.getName(), scheduleId, jobInfo.getTenantId(), jobInfo.getTriggerLastTime(), jobInfo.getTriggerNextTime());
    }
}
